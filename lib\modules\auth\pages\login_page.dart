import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/widgets/app_button.dart';
import '../../../shared/widgets/app_input.dart';
import '../controllers/auth_controller.dart';

class LoginPage extends StatelessWidget {
  LoginPage({super.key});

  // Put AuthController instead of LoginController
  final AuthController authController = Get.find<AuthController>();

  // Local form key and controllers for textfields
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  final RxBool isLoading = false.obs;

  void _login() async {
    if (!formKey.currentState!.validate()) return;
    isLoading.value = true;
    try {
      await authController.login(
        emailController.text.trim(),
        passwordController.text.trim(),
      );
      // If successful, navigate to home/dashboard
      Get.offAllNamed('/home');
    } catch (e) {
      Get.snackbar(
        "Login Failed",
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Icon(
                  Icons.lock_outline,
                  size: 80,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 16),
                Text(
                  "Welcome Back",
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const SizedBox(height: 32),

                // Email
                AppInput(
                  controller: emailController,
                  label: "Email",
                  hint: "Enter your email",
                  keyboardType: TextInputType.emailAddress,
                  prefixIcon: const Icon(Icons.email_outlined),
                  validator: (val) {
                    if (val == null || val.isEmpty) {
                      return "Email is required";
                    }
                    if (!GetUtils.isEmail(val)) {
                      return "Enter a valid email";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Password
                AppInput(
                  controller: passwordController,
                  label: "Password",
                  obscureText: true,
                  prefixIcon: const Icon(Icons.lock_outline),
                  validator: (val) {
                    if (val == null || val.isEmpty) {
                      return "Password is required";
                    }
                    if (val.length < 6) {
                      return "Password must be at least 6 characters";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Login Button
                Obx(() => AppButton(
                      label: "Login",
                      isLoading: isLoading.value,
                      onPressed: _login,
                      icon: Icons.login,
                    )),

                const SizedBox(height: 16),

                TextButton(
                  onPressed: () {
                    // TODO: Forgot Password Page
                  },
                  child: const Text("Forgot Password?"),
                ),
                TextButton(
                  onPressed: () {
                    Get.toNamed('/register');
                  },
                  child: const Text("Don’t have an account? Sign Up"),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
