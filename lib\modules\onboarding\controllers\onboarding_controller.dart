import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../data/models/system_check.dart';

class OnboardingController extends GetxController {
  final _storage = GetStorage();

  var checks = <SystemCheck>[
    SystemCheck(id: "network", label: "Network Connection", icon: "wifi"),
    SystemCheck(id: "hardware", label: "Hardware Verification", icon: "computer"),
    SystemCheck(id: "database", label: "Database Connection", icon: "storage"),
    SystemCheck(id: "security", label: "Security Protocols", icon: "shield"),
  ].obs;

  // toggle for testing
  bool get isOnboardingComplete => _storage.read('onboarding_complete') ?? false;

  @override
  void onInit() {
    super.onInit();
    _runChecks();
  }

  Future<void> _runChecks() async {
    for (int i = 0; i < checks.length; i++) {
      // Mark current as checking
      checks[i].status = CheckStatus.checking;
      checks.refresh();

      await Future.delayed(Duration(milliseconds: 1000 + (500 * i)));

      // <PERSON> completed
      checks[i].status = CheckStatus.completed;
      checks.refresh();
    }

    // Navigate after short delay
    await Future.delayed(const Duration(seconds: 1));
    completeOnboarding();
  }

  IconData getIcon(String iconName) {
    switch (iconName) {
      case "wifi":
        return Icons.wifi;
      case "computer":
        return Icons.computer;
      case "storage":
        return Icons.storage;
      case "shield":
        return Icons.shield;
      default:
        return Icons.check_circle;
    }
  }

  void completeOnboarding() {
    // _storage.write('onboarding_complete', true);
    Get.offAllNamed('/home');
  }

  void resetOnboarding() {
    // For testing
    _storage.remove('onboarding_complete');
  }
}
