import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../modules/auth/pages/login_page.dart';
import '../modules/auth/pages/splash_page.dart';
import '../modules/auth/pages/register_page.dart';
import '../modules/home/<USER>/home_page.dart';
import '../modules/auth/bindings/auth_binding.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/auth/controllers/auth_controller.dart';
import '../modules/onboarding/pages/country_page.dart';
import '../modules/onboarding/bindings/onboarding_binding.dart';
import '../modules/onboarding/pages/slides_page.dart';

class AuthGuard extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    try {
      final auth = Get.find<AuthController>();
      if (!auth.isAuthenticated) {
        return const RouteSettings(name: '/login');
      }
    } catch (_) {
      return const RouteSettings(name: '/login');
    }
    return null;
  }
}

final routes = [
  GetPage(name: '/', page: () => const SplashPage(), binding: AuthBinding()),
  GetPage(
    name: '/onboarding/country',
    page: () => CountryPage(),
    binding: OnboardingBinding(),
  ),
  GetPage(
    name: '/onboarding/slides',
    page: () => const SlidesPage(),
    binding: OnboardingBinding(),
  ),
  GetPage(name: '/login', page: () => LoginPage(), binding: AuthBinding()),
  GetPage(
    name: '/register',
    page: () => RegisterPage(),
    binding: AuthBinding(),
  ),
  GetPage(
    name: '/home',
    page: () => HomePage(),
    binding: HomeBinding(),
    middlewares: [AuthGuard()],
  ),
];
